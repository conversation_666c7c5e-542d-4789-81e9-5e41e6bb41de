package com.ybm100.app.crm.global;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.Application;
import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.multidex.MultiDex;

import com.alibaba.android.arouter.launcher.ARouter;
import com.didichuxing.doraemonkit.kit.AbstractKit;
import com.iflytek.cloud.Setting;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechUtility;
import com.quick.qt.analytics.QtTrackAgent;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.DefaultRefreshFooterCreator;
import com.scwang.smartrefresh.layout.api.DefaultRefreshHeaderCreator;
import com.scwang.smartrefresh.layout.api.RefreshFooter;
import com.scwang.smartrefresh.layout.api.RefreshHeader;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.constant.SpinnerStyle;
import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.tencent.bugly.Bugly;
import com.tencent.bugly.beta.Beta;
import com.tencent.bugly.beta.interfaces.BetaPatchListener;
import com.tencent.tinker.entry.DefaultApplicationLike;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.message.IUmengRegisterCallback;
import com.umeng.message.PushAgent;
import com.umeng.message.UmengMessageHandler;
import com.umeng.message.UmengNotificationClickHandler;
import com.umeng.message.entity.UMessage;
import com.umeng.socialize.Config;
import com.umeng.socialize.PlatformConfig;
import com.umeng.socialize.common.SocializeConstants;
import com.xyy.canary.AppUpdate;
import com.xyy.common.util.DeviceUtils;
import com.xyy.common.util.ScreenUtils;
import com.xyy.common.util.ToastUtils;
import com.xyy.flutter.container.container.ContainerConfig;
import com.xyy.flutter.container.container.ContainerRuntime;
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils;
import com.xyy.utilslibrary.AppManager;
import com.xyy.utilslibrary.UtilsLibraryApp;
import com.xyy.utilslibrary.global.CrashHandler;
import com.xyy.utilslibrary.global.GlobalApplication;
import com.xyy.utilslibrary.utils.AppUtils;
import com.ybm100.app.crm.BuildConfig;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.constant.AppNetConfig;
import com.ybm100.app.crm.constant.GlobalConstants;
import com.ybm100.app.crm.doraemon.SimulatedLoginKit;
import com.ybm100.app.crm.flutter.CustomFlutterActivity;
import com.ybm100.app.crm.flutter.channel.ApiEnvHandler;
import com.ybm100.app.crm.flutter.channel.AppHostHandler;
import com.ybm100.app.crm.flutter.channel.CallPhoneHandler;
import com.ybm100.app.crm.flutter.channel.CheckNotificationSettingHandler;
import com.ybm100.app.crm.flutter.channel.EventBusHandler;
import com.ybm100.app.crm.flutter.channel.EventTrackHandler;
import com.ybm100.app.crm.flutter.channel.HandleCallRecordHandler;
import com.ybm100.app.crm.flutter.channel.MapNavigationHandler;
import com.ybm100.app.crm.flutter.channel.OpenNotificationSettingHandler;
import com.ybm100.app.crm.flutter.channel.PhotoHandler;
import com.ybm100.app.crm.flutter.channel.PrivateListJumpHandler;
import com.ybm100.app.crm.flutter.channel.PublicListJumpHandler;
import com.ybm100.app.crm.flutter.channel.PurchasedJumpHandler;
import com.ybm100.app.crm.flutter.channel.ScheduleSelectImageHandler;
import com.ybm100.app.crm.flutter.channel.SearchDrivingLineHandler;
import com.ybm100.app.crm.flutter.channel.ShareHandler;
import com.ybm100.app.crm.flutter.channel.UserInfoHandler;
import com.ybm100.app.crm.flutter.channel.VoiceInputHandler;
import com.ybm100.app.crm.flutter.debug.FlutterKit;
import com.ybm100.app.crm.platform.RuntimeEnv;
import com.ybm100.app.crm.schedule.service.CallRecordManager;
import com.ybm100.app.crm.utils.BuglyUtil;
import com.ybm100.app.crm.utils.DeviceIdUtil;
import com.ybm100.app.crm.utils.GlobalRouterUtil;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.utils.SnowGroundUtils;
import com.ybm100.app.crm.utils.debug.DebugService;
import com.ybm100.app.crm.utils.deviceInfo.DeviceInfoReporter;
import com.ybm100.app.crm.utils.deviceInfo.collector.EmulatorCollector;
import com.quick.qt.commonsdk.QtConfigure;
import com.ybmmarket20.xyyreport.session.WatchAppLifecycle;

import org.android.agoo.huawei.HuaWeiRegister;
import org.android.agoo.xiaomi.MiPushRegistar;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import ly.count.android.sdk.XyyApmCly;


/**
 * @author: zcj
 * @time:2020/8/26. Description:
 */
public class MyApplicationLike extends DefaultApplicationLike implements UserBehaviorTrackingUtils.TrackingBaseInfo, Application.ActivityLifecycleCallbacks {

    private int mActivityCount = 0;
    private boolean isSnowGroundAppForegroundStarted = false;
    private Long mStartTime = 0L;

    public MyApplicationLike(Application application, int tinkerFlags, boolean tinkerLoadVerifyFlag, long applicationStartElapsedTime, long applicationStartMillisTime, Intent tinkerResultIntent) {
        super(application, tinkerFlags, tinkerLoadVerifyFlag, applicationStartElapsedTime, applicationStartMillisTime, tinkerResultIntent);
    }

    @Override
    public void onCreate() {
        super.onCreate();

        if (AppUtils.isMainProcess(getApplication())) {

            checkEmulator();
            CrashHandler.getInstance().init(getApplication());
            initSmartRefreshLayout();
            initFlutter();
            initRuntimeEnv();
            UtilsLibraryApp.initialize(getApplication(), BuildConfig.FLAVOR);
            GlobalApplication.init(getApplication());

            initARoute();
            //错误日志收集
            BuglyUtil.init(getApplication());
            initHotFix();
            initSpeech();
            cleanDirtyData();

            /**
             * 埋点初始化
             */
            UserBehaviorTrackingUtils.init(BuildConfig.BUILD_TYPE.equals("release") && BuildConfig.FLAVOR.equals("prod"),
                    "046720f420344f678fe234f4a728c309", "c10ccdcec4324136bf7abf98ce48d32d",
                    "xyyBean", getApplication(),
                    "https://msg.api.ybm100.com/snow", "http://app-v4.ybm100.com/app/snowground/deviceinfo", this);

            /**
             * Quick Tracking SDK预初始化
             * 用于满足隐私政策合规要求
             */
            initQuickTrackingPreInit();

            /**
             * AppForeground 埋点
             */
            registerActivityLifecycleCallback(this);


            boolean isProd = isAppProd();
            // 初始化Canary 更新SDK
            initAppUpdate(isProd);
            // 初始化countly apm SDK
            initApmCly(isProd);
            DeviceInfoReporter.INSTANCE.report(getApplication());
            checkYBMDeviceId();
            initDebugService();
        }

        initUmeng();
    }



    private void initDebugService() {
        if (!isAppProd()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getApplication().startForegroundService(new Intent(getApplication(), DebugService.class));
            } else {
                getApplication().startService(new Intent(getApplication(), DebugService.class));
            }
        }
    }

    private void checkYBMDeviceId() {
        DeviceIdUtil.INSTANCE.loadYBMDeviceId(getApplication());
    }

    private void checkEmulator() {
        if (new EmulatorCollector().isEmulator(getApplication())) {
            ToastUtils.showShort("设备异常，禁止启动豆芽");
            AppManager.getAppManager().finishAllActivity();
            Process.killProcess(Process.myPid());
            System.exit(0);
        }
    }

    private void initFlutter() {
        ContainerRuntime.INSTANCE.init(getApplication(), new ContainerConfig.Builder()
                .setContainerClass(CustomFlutterActivity.class)
                .setCacheEngineCount(10)
                .build());

        ContainerRuntime.INSTANCE.getBridge().setBridgeImpl(new FlutterBridgeImpl());
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("user_info", UserInfoHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("app_host", AppHostHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("event_track", EventTrackHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("purchased_jump", PurchasedJumpHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("private_list_jump", PrivateListJumpHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("public_list_jump", PublicListJumpHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("event_bus", EventBusHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("select_photo", PhotoHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("schedule_select_image", ScheduleSelectImageHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("voice_input", VoiceInputHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("search_driving_line", SearchDrivingLineHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("check_notification_setting", CheckNotificationSettingHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("open_notification_setting", OpenNotificationSettingHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("call_phone", CallPhoneHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("api_env", ApiEnvHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("handle_call_record", HandleCallRecordHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("map_navigation", MapNavigationHandler.class);
        ContainerRuntime.INSTANCE.getBridge().registerBridgeHandler("open_share", ShareHandler.class);
    }

    private boolean isAppProd() {
        String currFlavor = SharedPrefManager.getInstance().getCurrFlavor();
        if (TextUtils.isEmpty(currFlavor) || AppNetConfig.FlavorType.CUSTOM.equals(currFlavor)) {
            currFlavor = RuntimeEnv.INSTANCE.getEnv();
        }
        return AppNetConfig.FlavorType.PROD.equalsIgnoreCase(currFlavor);
    }

    private void initAppUpdate(boolean isProd) {
        AppUpdate.getInstance()
                .init(getApplication())
                .isRrodEnviroment(isProd)
                .setAppTenantId(isProd ? "100010" : "100009")
                .setAppChannel("official")
                .setAppVersionName(BuildConfig.VERSION_NAME)
                .setAppVersionCode(BuildConfig.VERSION_CODE)
                .setAppLauncher(R.mipmap.ic_launcher)
                .setMonitorListener((eventName, eventParams) -> {
                    SnowGroundUtils.track(eventName, eventParams);
                });
    }

    private void initApmCly(boolean isProd) {
        // 测试环境下：crm_test（appkey）
        final String APP_TEST_KEY = "9c862d8797401d43dd69e8f10decec17c85d18f8";
        // 正式环境下：crm（appkey）
        final String APP_KEY = "4f4f88d3c6d1455e88dc9ccc8d9df88e2888214e";

        String xyyUserId = "unlogin";
        String xyyUser = "unlogin";
        if (SharedPrefManager.getInstance().isLogin()) {
            xyyUser = SharedPrefManager.getInstance().getUserInfo().getName();
            xyyUserId = SharedPrefManager.getInstance().getUserInfo().getSysUserId();
        }

        XyyApmCly.getInstance()
                .setApplication(getApplication())
                .setAppKey(isProd ? APP_KEY : APP_TEST_KEY)
                .setDeviceId(DeviceUtils.getXyyDeviceUid())
                .setUser(xyyUser, xyyUserId)
                .initialize(isProd, isProd ? 20 : 1);
    }

    /**
     * 制定默认的header和footer
     */
    private static void initSmartRefreshLayout() {
        //设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator(new DefaultRefreshHeaderCreator() {
            @NonNull
            @Override
            public RefreshHeader createRefreshHeader(@NonNull Context context, @NonNull RefreshLayout layout) {
                return new ClassicsHeader(context);//指定为经典Header，默认是 贝塞尔雷达Header
            }
        });
        //设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator(new DefaultRefreshFooterCreator() {
            @NonNull
            @Override
            public RefreshFooter createRefreshFooter(@NonNull Context context, @NonNull RefreshLayout layout) {
                layout.setEnableLoadMoreWhenContentNotFull(true);//内容不满一页时候启用加载更多
                ClassicsFooter footer = new ClassicsFooter(context);
                footer.setBackgroundResource(android.R.color.white);
                footer.setSpinnerStyle(SpinnerStyle.FixedBehind);//设置为拉伸模式
                return footer;//指定为经典Footer，默认是 BallPulseFooter
            }
        });
    }

    private void initHotFix() {
        // 设置是否开启热更新能力，默认为true
        Beta.enableHotfix = true;
        // 设置是否自动下载补丁，默认为true
        Beta.canAutoDownloadPatch = true;
        // 设置是否自动合成补丁，默认为true
        Beta.canAutoPatch = true;
        // 设置是否提示用户重启，默认为false
        Beta.canNotifyUserRestart = true;
        // 补丁回调接口
        Beta.betaPatchListener = new BetaPatchListener() {
            @Override
            public void onPatchReceived(String patchFile) {
                Toast.makeText(getApplication(), "补丁下载地址" + patchFile, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onDownloadReceived(long savedLength, long totalLength) {
                Toast.makeText(getApplication(),
                        String.format(Locale.getDefault(), "%s %d%%",
                                Beta.strNotificationDownloading,
                                (int) (totalLength == 0 ? 0 : savedLength * 100 / totalLength)),
                        Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onDownloadSuccess(String msg) {
                Toast.makeText(getApplication(), "补丁下载成功", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onDownloadFailure(String msg) {
                Toast.makeText(getApplication(), "补丁下载失败", Toast.LENGTH_SHORT).show();

            }

            @Override
            public void onApplySuccess(String msg) {
                Toast.makeText(getApplication(), "补丁应用成功", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onApplyFailure(String msg) {
                Toast.makeText(getApplication(), "补丁应用失败", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onPatchRollback() {

            }
        };
        // 设置开发设备，默认为false，上传补丁如果下发范围指定为“开发设备”，需要调用此接口来标识开发设备
        Bugly.setIsDevelopmentDevice(getApplication(), BuildConfig.DEBUG);
    }

    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    @Override
    public void onBaseContextAttached(Context base) {
        super.onBaseContextAttached(base);
        // you must install multiDex whatever tinker is installed!
        MultiDex.install(base);
        Beta.installTinker(this);

    }

    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    public void registerActivityLifecycleCallback(
            Application.ActivityLifecycleCallbacks callbacks) {
        getApplication().registerActivityLifecycleCallbacks(callbacks);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        Beta.unInit();
    }

    /**
     * 清除本地残留的雪地埋点数据库
     */
    private void cleanDirtyData() {
        try {
            SharedPreferences sp = getApplication().getSharedPreferences("application", Context.MODE_PRIVATE);
            int oldVersion = sp.getInt("version", 0);
            int versionCode = AppUtils.getAppVersionCode(getApplication());
            if (oldVersion < versionCode) {
                //删除数据库
                CallRecordManager.INSTANCE.clearCallRecord();
                sp.edit().putInt("version", versionCode).apply();
                Log.e("cleanDirtyData", "clean");
            }
        } catch (Throwable ignore) {
            Log.e("cleanDirtyData", "error");
        }
    }

    private void initARoute() {
        if (BuildConfig.DEBUG) {           // 这两行必须写在init之前，否则这些配置在init过程中将无效
            ARouter.openLog();     // 打印日志
            ARouter.openDebug();   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        ARouter.init(getApplication()); // 尽可能早，推荐在Application中初始化
    }

    //讯飞语音识别sdk初始化
    private void initSpeech() {
        // 应用程序入口处调用，避免手机内存过小，杀死后台进程后通过历史intent进入Activity造成SpeechUtility对象为null
        // 注意：此接口在非主进程调用会返回null对象，如需在非主进程使用语音功能，请增加参数：SpeechConstant.FORCE_LOGIN+"=true"
        // 注意： appid 必须和下载的SDK保持一致，否则会出现10407错误
        SpeechUtility.createUtility(getApplication(), SpeechConstant.APPID + "=5ea56563");

        // 以下语句用于设置日志开关（默认开启），设置成false时关闭语音云SDK日志打印
        Setting.setShowLog(AppUtils.isApkInDebug(getApplication()));
    }

    /**
     * 初始化友盟
     */
    private void initUmeng() {
        /**
         * 初始化common库
         * 参数1:上下文，不能为空
         * 参数2:友盟 app key  已修改为新账号
         * 参数3:友盟 channel
         * 参数4:设备类型，UMConfigure.DEVICE_TYPE_PHONE 为手机、UMConfigure.DEVICE_TYPE_BOX为盒子，默认为手机
         * 参数5:Push推送业务的secret
         */

        UMConfigure.init(getApplication(), GlobalConstants.UMAppKey, "default", UMConfigure.DEVICE_TYPE_PHONE, BuildConfig.UMMessageSecret);
        //获取消息推送代理示例
        PushAgent mPushAgent = PushAgent.getInstance(getApplication());
        mPushAgent.setResourcePackageName("com.ybm100.app.crm");
        UmengMessageHandler messageHandler = new UmengMessageHandler() {

            @Override
            public Notification getNotification(Context context, UMessage msg) {
                Log.e("guan", "UMPush getNotification:" + msg.url);
                UserBehaviorTrackingUtils.track("Event-UMPush-Notification", getPushJsonBean(msg));
                return super.getNotification(context, msg);
            }
        };

        mPushAgent.setMessageHandler(messageHandler);

        /**
         * 请在自定义Application中调用此接口，如果在Activity中调用，当应用进程关闭情况下，设置无效；
         * UmengNotificationClickHandler是在BroadcastReceiver中被调用，因此若需启动Activity，需为Intent添加Flag：Intent.FLAG_ACTIVITY_NEW_TASK，否则无法启动Activity。
         */
        UmengNotificationClickHandler notificationClickHandler = new UmengNotificationClickHandler() {
            @Override
            public void launchApp(Context context, UMessage uMessage) {
                Log.e("guan", "UMPush launchApp:" + uMessage.url);
                try {
                    if (SharedPrefManager.getInstance().isLogin()) {
                        GlobalRouterUtil.Companion.openUrl(context, uMessage.extra.get("url"));
                    } else {
                        super.launchApp(context, uMessage);
                    }
                } catch (Exception e) {
                    super.launchApp(context, uMessage);
                    e.printStackTrace();
                }

                UserBehaviorTrackingUtils.track("Event-UMPush-didReceiveNotification", getPushJsonBean(uMessage));
            }

            @Override
            public void dismissNotification(Context context, UMessage uMessage) {
                super.dismissNotification(context, uMessage);

                UserBehaviorTrackingUtils.track("Event-UMPush-DismissNotification", getPushJsonBean(uMessage));
            }

        };
        mPushAgent.setNotificationClickHandler(notificationClickHandler);

        //注册推送服务，每次调用register方法都会回调该接口
        mPushAgent.register(new IUmengRegisterCallback() {

            @Override
            public void onSuccess(String deviceToken) {
                //注册成功会返回deviceToken deviceToken是推送消息的唯一标志
                Log.e("guan", "UMPush deviceToken:" + deviceToken);
            }

            @Override
            public void onFailure(String s, String s1) {
                Log.e("guan", "UMPush deviceToken:failed" + s + "," + s1);
            }
        });

        //小米通道
        MiPushRegistar.register(getApplication(), BuildConfig.UMXiaoMiAppID, BuildConfig.UMXiaoMiAppKey);
        //华为通道，注意华为通道的初始化参数在minifest中配置
        HuaWeiRegister.register(getApplication());

        //对应平台没有安装的时候跳转转到应用商店下载
        Config.isJumptoAppStore = true;
        //在debug版本下获取版本号
        if (BuildConfig.DEBUG) {
            String sdkVersion = SocializeConstants.SDK_VERSION;//获取友盟的版本号
            Log.e("UMPush", "UMPush=============" + sdkVersion);
            /**
             * 设置组件化的Log开关
             * 参数: boolean 默认为false，如需查看LOG设置为true
             */
            UMConfigure.setLogEnabled(true);
        } else {
            UMConfigure.setLogEnabled(false);
            //会对日志进行加密，有效防止网络攻击，提高数据安全性
            UMConfigure.setEncryptEnabled(true);
        }
        PlatformConfig.setWeixin("wxba6a9c09f52d2b18", "684b333dee07717d8bc3a5364bf67b65");
        PlatformConfig.setWXFileProvider(BuildConfig.APPLICATION_ID + ".fileprovider");
        PlatformConfig.setWXWork("ww97630ccc40bc5795", "rgvsRVGvwNoX7EZEuVVj0BOSExEU_9cBOZxrOQxsUXQ", "1000030", "wwauth97630ccc40bc5795000030");
        PlatformConfig.setWXWorkFileProvider(BuildConfig.APPLICATION_ID + ".fileprovider");
    }

    @NotNull
    private JSONObject getPushJsonBean(UMessage uMessage) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("um_msg_id", uMessage.msg_id);
            jsonObject.put("um_msg_title", uMessage.title);
            jsonObject.put("um_msg_text", uMessage.text);
            jsonObject.put("um_msg_url", uMessage.extra.get("url"));
            String oaId = "";
            UserInfoBean infoBean = SharedPrefManager.getInstance().getUserInfo();
            if (infoBean != null) {
                oaId = infoBean.getSysUserId();
            }
            jsonObject.put("oaId", oaId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject;
    }


    private void initRuntimeEnv() {
        RuntimeEnv.INSTANCE.init(new RuntimeEnv.EnvCallback() {

            @NotNull
            @Override
            public String getH5StageHost() {
                return BuildConfig.H5_URL_STAGE;
            }

            @NotNull
            @Override
            public String getH5TestHost() {
                return BuildConfig.H5_URL_TEST;
            }

            @NotNull
            @Override
            public String getStageHost() {
                return BuildConfig.API_URL_STAGE;
            }

            @NotNull
            @Override
            public String getDevHost() {
                return BuildConfig.API_URL_DEV;
            }

            @NotNull
            @Override
            public String getTestHost() {
                return BuildConfig.API_URL_TEST;
            }

            @NotNull
            @Override
            public Application getAppContext() {
                return getApplication();
            }

            @NotNull
            @Override
            public String getBuildType() {
                return BuildConfig.BUILD_TYPE;
            }

            @NotNull
            @Override
            public String getEnv() {
                return BuildConfig.FLAVOR;
            }

            @NotNull
            @Override
            public String getApiHost() {
                return BuildConfig.API_URL;
            }

            @NotNull
            @Override
            public String getImageHost() {
                return BuildConfig.CDN_URL;
            }

            @NotNull
            @Override
            public String getH5Host() {
                return BuildConfig.H5_URL;
            }

            @Override
            public boolean isDebug() {
                return BuildConfig.DEBUG;
            }

            @NotNull
            @Override
            public String getApplicationId() {
                return BuildConfig.APPLICATION_ID;
            }

            @NotNull
            @Override
            public List<AbstractKit> getDebugKitList() {
                ArrayList<AbstractKit> abstractKits = new ArrayList<>();
                abstractKits.add(new FlutterKit());
                abstractKits.add(new SimulatedLoginKit());
                return abstractKits;
            }

            @NotNull
            @Override
            public String getProviderName() {
                return BuildConfig.PROVIDER_NAME;
            }
        }, new RuntimeEnv.CompatCallback() {
            @Override
            public boolean getResetTabWhenTabLayoutRefresh() {
                return false;
            }

        });
    }

    //设备厂商
    @Nullable
    @Override
    public String getDeviceBrand() {
        String brand;
        try {
            brand = Build.BRAND;
        } catch (Exception e) {
            e.printStackTrace();
            brand = "";
        }
        return brand;
    }

    //设备型号
    @Nullable
    @Override
    public String getDeviceModel() {
        String model;
        try {
            model = Build.MODEL;
        } catch (Exception e) {
            e.printStackTrace();
            model = "";
        }
        return model;
    }

    //分辨率
    @Nullable
    @Override
    public String getDeviceResolution() {
        return ScreenUtils.getScreenWidth() + "X" + ScreenUtils.getScreenHeight();
    }

    //账号
    @Nullable
    @Override
    public String getUserAccount() {
        return SharedPrefManager.getInstance().getUserInfo().getName();
    }

    //角色
    @Nullable
    @Override
    public String getUserRoleType() {
        return String.valueOf(SharedPrefManager.getInstance().getUserInfo().getRoleType());
    }

    @Nullable
    @Override
    public String getOAID() {
        return SharedPrefManager.getInstance().getUserInfo().getSysUserId();
    }

    //设备ID
    @Nullable
    @Override
    public String getDeviceID() {
        return DeviceUtils.getDeviceId();
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {

    }

    @Override
    public void onActivityStarted(Activity activity) {
        mActivityCount++;
        if (mActivityCount == 1 && !isSnowGroundAppForegroundStarted) {
            isSnowGroundAppForegroundStarted = true;

            mStartTime = System.currentTimeMillis();

            UserBehaviorTrackingUtils.startTrack("AppForeground");
            CallRecordManager.INSTANCE.handleCallRecord("AppForeground");
        }
    }

    @Override
    public void onActivityResumed(Activity activity) {

    }

    @Override
    public void onActivityPaused(Activity activity) {

    }

    @Override
    public void onActivityStopped(Activity activity) {
        mActivityCount--;
        if (mActivityCount == 0 && isSnowGroundAppForegroundStarted) {
            isSnowGroundAppForegroundStarted = false;

            if (mStartTime > 0) {
                Double timeInterval = (System.currentTimeMillis() - mStartTime) / 1000.0D;

                UserBehaviorTrackingUtils.endTrack("AppForeground", timeInterval, "foregroundTime");

                mStartTime = System.currentTimeMillis();
            }
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {

    }

    /**
     * Quick Tracking SDK预初始化
     * 根据阿里云官方文档进行合规初始化
     */
    private void initQuickTrackingPreInit() {
        try {
            QtConfigure.resetStorePath();
            // 4. 调用预初始化方法
            String appKey = getQuickTrackingAppKey();
            String channel = getQuickTrackingChannel();
            QtConfigure.setCustomDomain("https://qt.ybm100.com", null);
            QtConfigure.setLogEnabled(true); // 打开SDK调试日志
            QtConfigure.preInit(this.getApplication(), appKey, "");
            QtConfigure.setLogEnabled(true);
            android.util.Log.e("QuickTrackingSDK", "预初始化成功");
            initQuickTrackingFormal(appKey,channel);
        } catch (Exception e) {
            android.util.Log.e("QuickTrackingSDK", "预初始化失败", e);
        }
    }

    /**
     * 获取Quick Tracking AppKey
     * 根据当前环境返回对应的AppKey
     */
    private String getQuickTrackingAppKey() {
        if (isAppProd()) {
            // 生产环境AppKey - 请替换为您在阿里云控制台申请的生产环境AppKey
            return "3p07vcxxj6fo1wmocilsopv2";
        } else {
            // 测试环境AppKey - 请替换为您在阿里云控制台申请的测试环境AppKey
            return "gp4ewsfsnmg347oiji11rbyg";
        }
    }

    /**
     * 获取Quick Tracking渠道
     */
    private String getQuickTrackingChannel() {
        if (isAppProd()) {
            return "production"; // 生产环境渠道
        } else {
            return "test"; // 测试环境渠道
        }
    }

    /**
     * 获取Quick Tracking收数域名
     * 注意：此域名必须设置，不能为空，否则SDK会抛出异常
     */
    private String getQuickTrackingDomain() {
        return "https://qt.ybm100.com";
    }

    /**
     * 获取Quick Tracking备用收数域名
     * 返回null表示不使用备用域名
     */
    private String getQuickTrackingBackupDomain() {
        // 不使用备用域名，返回null
        return null;
    }

    /**
     * Quick Tracking SDK正式初始化
     * 只有在用户同意隐私政策后才能调用
     */
    private void initQuickTrackingFormal(String appKey, String channel) {
        try {
            QtConfigure.setCustomDomain("https://qt.ybm100.com", null);
            // 调用正式初始化方法
            QtConfigure.init(this.getApplication(),appKey,channel, QtConfigure.DEVICE_TYPE_PHONE, "");
            WatchAppLifecycle.get().register();
            QtTrackAgent.disableActivityPageCollection();
            android.util.Log.i("QuickTrackingSDK", "正式初始化成功 - AppKey: " + appKey + ", Channel: " + channel);
        } catch (Exception e) {
            android.util.Log.e("QuickTrackingSDK", "正式初始化失败", e);
        }
    }

}
