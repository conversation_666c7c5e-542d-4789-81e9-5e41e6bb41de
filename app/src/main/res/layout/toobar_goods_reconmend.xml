<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/default_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/nav_return">

        <TextView
            android:id="@+id/tv_toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="#333333"
            android:textSize="17sp"
            tools:text="标题标题" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:layout_marginEnd="15dp"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_right_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp15"
                android:src="@drawable/icon_search"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/iv_right_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/nav_shaixuan"
                android:visibility="gone" />
        </LinearLayout>

    </androidx.appcompat.widget.Toolbar>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/default_toolbar"
        android:background="#FFE4E5EA"
        app:layout_constraintTop_toBottomOf="@id/default_toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>