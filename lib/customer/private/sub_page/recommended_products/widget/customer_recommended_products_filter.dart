import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class CustomerRecommendedProductsFilter extends StatefulWidget {
  final ValueChanged<Map<String, dynamic>> changeFilter;

  CustomerRecommendedProductsFilter({required this.changeFilter});
  @override
  State<StatefulWidget> createState() {
    return CustomerRecommendedProductsFilterState();
  }
}

class CustomerRecommendedProductsFilterState extends State<CustomerRecommendedProductsFilter> {

  final List<_OftenBuyButtonOptionModel> filterList = [
    _OftenBuyButtonOptionModel(title: "全部", option: -1),
    _OftenBuyButtonOptionModel(title: "优选品", option: 1),
    _OftenBuyButtonOptionModel(title: "控销品", option: 2),
    _OftenBuyButtonOptionModel(title: "甄选品", option: 3)
  ];

  ValueNotifier<BackgroundButtonState> curController =
      ValueNotifier<BackgroundButtonState>(BackgroundButtonState.selected);

  Map<String, dynamic> params = {};

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      color: Color(0xFFFFFFFF),
      child: Row(
        children: this.getFilterItems(),
      ),
    );
  }

  List<Widget> getFilterItems() {
    List<Widget> child = [];
    child.addAll(filterList
        .mapIndexed(
          (index, element) => Container(
            padding: EdgeInsets.fromLTRB(10, 10, 0, 10),
            child: BackgroundStateButton(
              onPressed: selectItem,
              title: element.title,
              option: element.option,
              controller: index == 0 ? this.curController : null,
              alignment: Alignment.center,
              panding: EdgeInsets.only(left: 15, right: 15),
              selectStyle: TextStyle(
                color: Color(0xFF00B377),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textStyle: TextStyle(
                color: Color(0xFF676773),
                fontSize: 12,
              ),
              normalDecoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: Color(0xFFF7F7F8),
              ),
              selectDecoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Color(0xFF00B377), width: 0.5),
                color: Color(0xFFFFFFFF),
              ),
            ),
          ),
        )
        .toList());
    child.add(Spacer());
    child.add(SizedBox(width: 15));
    return child;
  }

  void selectItem(
      ValueNotifier<BackgroundButtonState> controller, dynamic option) {
    if (controller == curController) {
      return;
    }
    curController.value = BackgroundButtonState.normal;
    curController = controller;
    if (option == -1) {
      this.params.remove("queryType");
    } else {
      this.params["queryType"] = option;
    }

    widget.changeFilter(this.params);
  }
}

class _OftenBuyButtonOptionModel {
  final String title;
  final dynamic option;
  _OftenBuyButtonOptionModel({required this.title, this.option});
}
