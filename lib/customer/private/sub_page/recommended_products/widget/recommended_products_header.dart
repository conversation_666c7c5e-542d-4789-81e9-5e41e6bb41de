import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class RecommendedProductsHeader extends Header {
  @override
  Widget contentBuilder(
      BuildContext context,
      RefreshMode refreshState,
      double pulledExtent,
      double refreshTriggerPullDistance,
      double refreshIndicatorExtent,
      AxisDirection axisDirection,
      bool float,
      Duration? completeDuration,
      bool enableInfiniteRefresh,
      bool success,
      bool noMore) {
    
    Widget child;
    
    if (refreshState == RefreshMode.refresh || refreshState == RefreshMode.armed) {
      child = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00B377)),
            ),
          ),
          SizedBox(width: 10),
          Text(
            '正在刷新...',
            style: TextStyle(color: Color(0xFF666666), fontSize: 14),
          ),
        ],
      );
    } else if (refreshState == RefreshMode.refreshed) {
      child = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Color(0xFF00B377),
            size: 20,
          ),
          SizedBox(width: 8),
          Text(
            '刷新完成',
            style: TextStyle(color: Color(0xFF00B377), fontSize: 14),
          ),
        ],
      );
    } else if (refreshState == RefreshMode.done) {
      child = Text(
        success ? '刷新完成' : '刷新失败',
        style: TextStyle(color: Color(0xFF666666), fontSize: 14),
      );
    } else {
      // 默认状态：下拉刷新
      double progress = (pulledExtent / refreshTriggerPullDistance).clamp(0.0, 1.0);
      child = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Transform.rotate(
            angle: progress * 3.14159, // 旋转箭头
            child: Icon(
              Icons.arrow_downward,
              color: Color(0xFF666666),
              size: 20,
            ),
          ),
          SizedBox(width: 8),
          Text(
            progress >= 1.0 ? '释放刷新' : '下拉刷新',
            style: TextStyle(color: Color(0xFF666666), fontSize: 14),
          ),
        ],
      );
    }
    
    return Container(
      height: 60,
      alignment: Alignment.center,
      child: child,
    );
  }
}
