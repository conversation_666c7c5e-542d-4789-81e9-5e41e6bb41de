// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_recommended_products_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecommendedProductsOther _$RecommendedProductsOtherFromJson(
    Map<String, dynamic> json) =>
    RecommendedProductsOther(
      requestId: json['requestId'] as String?,
      qt_list_data: json['qt_list_data'] as String?,
    );

Map<String, dynamic> _$RecommendedProductsOtherToJson(
    RecommendedProductsOther instance) =>
    <String, dynamic>{
      'requestId': instance.requestId,
      'qt_list_data': instance.qt_list_data,
    };

RecommendedProductsResponseModel _$RecommendedProductsResponseModelFromJson(
    Map<String, dynamic> json) {
  return RecommendedProductsResponseModel()
    ..total = json['total']
    ..offset = json['offset']
    ..limit = json['limit']
    ..rows = (json['rows'] as List<dynamic>?)
            ?.map((e) =>
                CommodityDetailGoodItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        []
    ..pageCount = json['pageCount']
    ..lastPage = json['lastPage']
    ..currentPage = json['currentPage']
    ..currentPageTmp = json['currentPageTmp']
    ..requestParameters = json['requestParameters']
    ..requestUrl = json['requestUrl']
    ..unReadCount = json['unReadCount']
    ..other = json['other'] == null
        ? null
        : RecommendedProductsOther.fromJson(json['other'] as Map<String, dynamic>)
    ..otherTotal = json['otherTotal'];
}

Map<String, dynamic> _$RecommendedProductsResponseModelToJson(
    RecommendedProductsResponseModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'offset': instance.offset,
      'limit': instance.limit,
      'rows': instance.rows,
      'pageCount': instance.pageCount,
      'lastPage': instance.lastPage,
      'currentPage': instance.currentPage,
      'currentPageTmp': instance.currentPageTmp,
      'requestParameters': instance.requestParameters,
      'requestUrl': instance.requestUrl,
      'unReadCount': instance.unReadCount,
      'other': instance.other?.toJson(),
      'otherTotal': instance.otherTotal,
    };
