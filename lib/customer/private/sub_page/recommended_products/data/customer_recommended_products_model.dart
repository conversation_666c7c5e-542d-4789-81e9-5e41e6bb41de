import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_rank_data.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_recommended_products_model.g.dart';

@JsonSerializable()
class RecommendedProductsOther {
  /// 请求ID
  String? requestId;
  ///埋点qt_list_data
  String? qt_list_data;

  RecommendedProductsOther({this.requestId,this.qt_list_data});

  factory RecommendedProductsOther.fromJson(Map<String, dynamic> json) =>
      _$RecommendedProductsOtherFromJson(json);

  Map<String, dynamic> toJson() => _$RecommendedProductsOtherToJson(this);
}

@JsonSerializable()
class RecommendedProductsResponseModel extends BaseModelV2<RecommendedProductsResponseModel> {
  /// 总数量
  dynamic total;

  /// 偏移量
  dynamic offset;

  /// 限制数量
  dynamic limit;

  /// 商品列表
  @JsonKey(defaultValue: [])
  List<CommodityDetailGoodItemModel>? rows;

  /// 页数
  dynamic pageCount;

  /// 是否最后一页
  dynamic lastPage;

  /// 当前页
  dynamic currentPage;

  /// 当前页临时
  dynamic currentPageTmp;

  /// 请求参数
  dynamic requestParameters;

  /// 请求URL
  dynamic requestUrl;

  /// 未读数量
  dynamic unReadCount;

  /// 其他数据
  RecommendedProductsOther? other;

  /// 其他总数
  dynamic otherTotal;

  RecommendedProductsResponseModel();

  factory RecommendedProductsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$RecommendedProductsResponseModelFromJson(json);

  @override
  RecommendedProductsResponseModel fromJsonMap(Map<String, dynamic> json) {
    return _$RecommendedProductsResponseModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$RecommendedProductsResponseModelToJson(this);
  }
}
