import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_rank_data.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_out_of_stock_items_model.g.dart';

@JsonSerializable()
class OutOfStockItemsOther {
  /// 请求ID
  String? requestId;
  String? qt_list_data;

  OutOfStockItemsOther({this.requestId,this.qt_list_data});

  factory OutOfStockItemsOther.fromJson(Map<String, dynamic> json) =>
      _$OutOfStockItemsOtherFromJson(json);

  Map<String, dynamic> toJson() => _$OutOfStockItemsOtherToJson(this);
}

@JsonSerializable()
class OutOfStockItemsResponseModel extends BaseModelV2<OutOfStockItemsResponseModel> {
  /// 总数量
  dynamic total;
  
  /// 偏移量
  dynamic offset;
  
  /// 限制数量
  dynamic limit;
  
  /// 商品列表
  @JsonKey(defaultValue: [])
  List<CommodityDetailGoodItemModel>? rows;
  
  /// 页数
  dynamic pageCount;
  
  /// 是否最后一页
  dynamic lastPage;
  
  /// 当前页
  dynamic currentPage;
  
  /// 当前页临时
  dynamic currentPageTmp;
  
  /// 请求参数
  dynamic requestParameters;
  
  /// 请求URL
  dynamic requestUrl;
  
  /// 未读数量
  dynamic unReadCount;
  
  /// 其他数据
  OutOfStockItemsOther? other;

  /// 其他总数
  dynamic otherTotal;

  OutOfStockItemsResponseModel();

  factory OutOfStockItemsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$OutOfStockItemsResponseModelFromJson(json);

  @override
  OutOfStockItemsResponseModel fromJsonMap(Map<String, dynamic> json) {
    return _$OutOfStockItemsResponseModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OutOfStockItemsResponseModelToJson(this);
  }
}
