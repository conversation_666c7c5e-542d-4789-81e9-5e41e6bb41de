name: XyyBeanSproutsFlutter
description: A new flutter module project.

publish_to: none

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact


version: 7.8.2




environment:
  sdk: '>=2.12.0 <3.0.0'

dependencies:


  # Container升级Flutter 2.0分支
  XYYContainer:
    git:
      url: **********************:mobile/native-common/Flutter/flutter-container.git
      ref: dev
#      ref: dev_upgrade_3.0
#    path: ../container

  shared_preferences: ^2.0.7

  flutter_easyrefresh: ^2.2.1

  # 图表库
  fl_chart: ^0.36.4

  flutter_easyloading: ^3.0.0
  provider: ^5.0.0

  # 时间戳格式化
  date_format: ^2.0.2

  #截图工具 flutter < 3.0
  screenshot: 1.3.0
  
  #图片缓存
  cached_network_image: ^3.0.0

  # 分组列表
  sticky_and_expandable_list: 1.0.0-nullsafety.1

  # 牛逼的image封装，https://github.com/fluttercandies/extended_image
  extended_image: ^4.0.0

  # Android返回键拦截处理
  back_button_interceptor: 5.0.0
  
  # 图片浏览器
  photo_view: ^0.13.0

  intl: ^0.17.0

  # 文本跑马灯
  marquee: ^2.2.0

  # 保存image到相册
  image_gallery_saver: '^1.7.1'

  #滑动删除cell
  flutter_slidable: ^1.3.2

#   百度地图插件
  # flutter_bmflocation: 3.1.0+1
  # flutter_baidu_mapapi_map: 3.1.0+1
  # flutter_baidu_mapapi_base: 3.1.0
  # flutter_baidu_mapapi_search: 3.1.0
  # flutter_baidu_mapapi_utils: 3.1.0+1

  flutter_baidu_mapapi_map:
    git:
      url: **********************:mobile/native-common/Flutter/flutter_baidu_mapapi_map.git
      ref: master
  flutter_baidu_mapapi_search:
    git:
      url: **********************:mobile/native-common/Flutter/flutter_baidu_mapapi_search.git
      ref: master
  flutter_baidu_mapapi_utils:
    git:
      url: **********************:mobile/native-common/Flutter/flutter_baidu_mapapi_utils.git
      ref: master
  flutter_baidu_mapapi_base:
    git:
      url: **********************:mobile/native-common/Flutter/flutter_baidu_mapapi_base.git
      ref: master
  flutter_bmflocation:
    git:
      url: **********************:mobile/native-common/Flutter/flutter_bmflocation.git
      ref: master

  # 键盘
  keyboard_actions: ^3.4.7


  flutter:
    sdk: flutter

  #集成 QT
  qt_common_sdk: 2.0.4
  
  flutter_localizations:
    sdk: flutter


dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^1.12.2
  json_serializable: ^4.1.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add Flutter specific assets to your application, add an assets section,
  # like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  assets:
    - assets/images/
    - assets/images/titlebar/
    - assets/images/base/
    - assets/images/order/
    - assets/images/task/
    - assets/images/schedule/
    - assets/images/schedule/marker_normal/
    - assets/images/schedule/marker_warning/
    - assets/images/schedule/
    - assets/images/licence/
    - assets/images/visit/
    - assets/images/visit/voice/
    - assets/images/message/
    - assets/sources/schedule/
    - assets/images/funnel/
    - assets/images/customer/
    - assets/images/customer/map/
    - assets/images/customer/map/2.0x/
    - assets/images/customer/map/3.0x/
    - assets/images/commodity/
    - assets/images/pop_data/
    - assets/images/tab/
    - assets/images/home/
    - assets/images/quick/
    - assets/images/mine/
    - assets/images/goods/
    - assets/images/performance/
    - assets/images/share/
    - assets/images/business/
    - assets/images/price/
    - assets/images/apply/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages


  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.ybm100.app.crm.flutter
    iosBundleIdentifier: com.example.XyyBeanSproutsFlutter
