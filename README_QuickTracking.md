# Quick Tracking SDK ����˵��

## ����
����Ŀ�ѳɹ����ɰ�����Quick Trackingͳ��SDK���汾Ϊ2.0.4��

## ���ɲ���

### 1. ��������
�� `pubspec.yaml` �������������
```yaml
qt_common_sdk: 2.0.4
```

### 2. Android������

#### 2.1 Ԥ��ʼ��
�� `MyApplicationLike.java` �� `onCreate()` �����������Ԥ��ʼ�����룺
```java
private void initQuickTrackingSDK() {
    try {
        // ���滻Ϊ����ʵ��appkey����������
        String androidAppKey = "your_android_app_key_here";
        String channel = "your_channel_here";
        
        // ����Ԥ��ʼ������
        com.quick.qt.analytics.QtConfigure.preInit(getApplication(), androidAppKey, channel);
        
        Log.d("QuickTracking", "SDKԤ��ʼ���ɹ�");
    } catch (Exception e) {
        Log.e("QuickTracking", "SDKԤ��ʼ��ʧ��: " + e.getMessage());
    }
}
```

#### 2.2 ��������
�� `proguard-rules.pro` ������ӻ������ã�
```proguard
# Quick Tracking SDK
-keep class com.quick.qt.** {*;}
-keep class rpk.quick.qt.** {*;}
-dontwarn com.quick.qt.analytics.middle.DevLog

-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep public class com.ybm100.app.crm.R$*{
public static final int *;
}
```

### 3. Flutter������

#### 3.1 ����SDK
�� `main.dart` ������ӵ��룺
```dart
import 'package:qt_common_sdk/qt_common_sdk.dart';
```

#### 3.2 ��ʼ��
�� `main()` ����������˳�ʼ�����룺
```dart
void main() {
  // Quick Tracking SDK ��ʼ��
  initQuickTrackingSDK();
  // ... ��������
}

void initQuickTrackingSDK() async {
  try {
    // ���滻Ϊ����ʵ��appkey����������
    String androidAppKey = 'your_android_app_key_here';
    String iosAppKey = 'your_ios_app_key_here';
    String channel = 'your_channel_here';
    
    // ��ʽ��ʼ��SDK
    await QTCommonSdk.initCommon(androidAppKey, iosAppKey, channel);
    
    // ������־���أ������׶ο��Կ�������ʽ����ǰ��Ҫ�رգ�
    QTCommonSdk.setLogEnabled(true);
    
    print('Quick Tracking SDK ��ʼ���ɹ�');
  } catch (e) {
    print('Quick Tracking SDK ��ʼ��ʧ��: $e');
  }
}
```

## ʹ�÷���

### 1. ҳ��ͳ��
```dart
import 'package:XyyBeanSproutsFlutter/utils/quick_tracking_helper.dart';

// ҳ�濪ʼͳ��
QuickTrackingHelper.onPageStart('HomePage');

// ҳ�����ͳ��
QuickTrackingHelper.onPageEnd('HomePage');
```

### 2. �Զ����¼�ͳ��
```dart
// ���¼�
QuickTrackingHelper.onEvent('button_click', {
  'button_name': 'login_button',
  'page': 'login_page',
});

// ��ҳ�����Ƶ��¼�
QuickTrackingHelper.onEventWithPage('product_view', 'ProductDetailPage', {
  'product_id': '12345',
  'product_name': '������Ʒ',
  'category': '���Ӳ�Ʒ',
});
```

### 3. �û���Ϊͳ��
```dart
// �û���¼
QuickTrackingHelper.onProfileSignIn('user_12345');

// �û��ǳ�
QuickTrackingHelper.onProfileSignOff();
```

### 4. ҳ����������
```dart
QuickTrackingHelper.setPageProperty('ProductListPage', {
  'category': '�ֻ�����',
  'sort_type': 'price_asc',
  'filter_brand': 'Apple',
});
```

### 5. ȫ����������
```dart
// ע��ȫ������
QuickTrackingHelper.registerGlobalProperties({
  'user_type': 'vip',
  'app_version': '1.0.0',
  'device_type': 'android',
});

// ɾ���ض�ȫ������
QuickTrackingHelper.unregisterGlobalProperty('user_type');

// �������ȫ������
QuickTrackingHelper.clearGlobalProperties();
```

## ����˵��

### 1. AppKey����
�뽫���´����е�ռλ���滻Ϊ����ʵ��AppKey��
- Android�ˣ�`MyApplicationLike.java` �е� `androidAppKey`
- Flutter�ˣ�`main.dart` �е� `androidAppKey` �� `iosAppKey`
- ����������`channel`

### 2. ��˽�Ϲ�
�����ĵ�Ҫ����ȷ����
1. ���û�ͬ����˽���ߺ�ŵ�����ʽ��ʼ��
2. ����˽������˵��SDK�ռ��豸��Ϣ��Ŀ��
3. �ṩ�û�ѡ���˳��Ļ���

### 3. ��־����
- �����׶Σ�`QTCommonSdk.setLogEnabled(true)`
- ��ʽ������`QTCommonSdk.setLogEnabled(false)`

## ע������

1. **AppKeyһ����**��Android��Ԥ��ʼ����AppKey��Flutter����ʽ��ʼ����AppKey������ȫһ��
2. **��������һ����**��Android�˺�Flutter�˵���������������ȫһ��
3. **��˽�Ϲ�**��ȷ�����û�ͬ����˽���ߺ�Ž�����ʽ��ʼ��
4. **��־����**����ʽ����ǰ��ر���־����
5. **��������**��ȷ������������ȷ������SDK�����쳣

## ����ļ�

- `lib/utils/quick_tracking_helper.dart` - SDK������
- `lib/utils/quick_tracking_example.dart` - ʹ��ʾ��
- `app/src/main/java/com/ybm100/app/crm/global/MyApplicationLike.java` - Android��Ԥ��ʼ��
- `app/proguard-rules.pro` - ��������

## ����֧��

�������⣬��ο���
- [Quick Tracking Flutter SDK �ٷ��ĵ�](https://help.aliyun.com/document_detail/2640317.html)
- ��Ŀ�е�ʾ������
- ����̨��־���
